import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000'

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`)
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// API functions
export const apiService = {
  // Database stats
  getDatabaseStats: () => api.get('/api/stats/database'),

  // URL preview and game storage
  previewUrl: (url) => api.post('/api/preview', { url }),
  previewUrlDetailed: (url) => api.post('/api/preview/detailed', { url }),
  storeGame: (url, selectedStats = null) =>
    api.post('/api/games/store', { url, selected_stats: selectedStats }),

  // Games
  getGames: (sport = null, limit = 50) =>
    api.get('/api/games', { params: { sport, limit } }),

  // Players
  searchPlayers: (query, sport = null, limit = 20) =>
    api.get('/api/players/search', { params: { query, sport, limit } }),
  getPlayerStats: (playerId, statTypes = null) =>
    api.get(`/api/players/${playerId}/stats`, { params: { stat_types: statTypes } }),

  // Teams
  searchTeams: (query, sport = null, limit = 20) =>
    api.get('/api/teams/search', { params: { query, sport, limit } }),

  // Sports
  getAvailableSports: () => api.get('/api/sports'),
  getSportStats: (sport) => api.get(`/api/sports/${sport}/stats`),
  getMLBRecommendedStats: () => api.get('/api/mlb/recommended-stats'),

  // Analytics
  getTopPerformers: (sport, stat, limit = 10) =>
    api.get('/api/analytics/top-performers', { params: { sport, stat, limit } }),
  predictGame: (team1, team2, sport) =>
    api.post('/api/analytics/predict-game', { team1, team2, sport }),
  getTeamAverages: (teamName, sport, lastNGames = 10) =>
    api.get('/api/analytics/team-averages', { params: { team_name: teamName, sport, last_n_games: lastNGames } }),
}

export default api
