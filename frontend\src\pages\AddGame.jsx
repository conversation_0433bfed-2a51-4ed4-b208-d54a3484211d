import React, { useState, useEffect } from 'react'
import { Link2, CheckCircle, AlertCircle, Loader2, Info } from 'lucide-react'
import { apiService } from '../services/api'
import toast from 'react-hot-toast'

const AddGame = () => {
  const [url, setUrl] = useState('')
  const [preview, setPreview] = useState(null)
  const [selectedStats, setSelectedStats] = useState({})
  const [loading, setLoading] = useState(false)
  const [storing, setStoring] = useState(false)
  const [mlbRecommended, setMlbRecommended] = useState(null)
  const [nbaRecommended, setNbaRecommended] = useState(null)
  const [detailedPreview, setDetailedPreview] = useState(null)
  const [loadingDetails, setLoadingDetails] = useState(false)

  useEffect(() => {
    // Load recommended stats for all sports
    loadRecommendedStats()
  }, [])

  const loadRecommendedStats = async () => {
    try {
      const [mlbResponse, nbaResponse] = await Promise.all([
        apiService.getMLBRecommendedStats(),
        apiService.getNBARecommendedStats()
      ])
      setMlbRecommended(mlbResponse.data)
      setNbaRecommended(nbaResponse.data)
    } catch (error) {
      console.error('Error loading recommended stats:', error)
    }
  }

  const handlePreview = async () => {
    if (!url.trim()) {
      toast.error('Please enter a valid ESPN URL')
      return
    }

    try {
      setLoading(true)
      const response = await apiService.previewUrl(url.trim())
      setPreview(response.data)

      // Auto-select recommended stats based on sport
      if (response.data.sport === 'mlb' && mlbRecommended) {
        setSelectedStats({
          player_stats: mlbRecommended.player_stats
        })
      } else if (response.data.sport === 'nba' && nbaRecommended) {
        setSelectedStats({
          player_stats: nbaRecommended.player_stats,
          team_stats: nbaRecommended.team_stats
        })
      }

      toast.success('URL preview loaded successfully!')
    } catch (error) {
      console.error('Error previewing URL:', error)
      toast.error('Failed to preview URL. Please check the URL and try again.')
      setPreview(null)
    } finally {
      setLoading(false)
    }
  }

  const handleDetailedPreview = async () => {
    if (!preview) {
      toast.error('Please preview the URL first')
      return
    }

    try {
      setLoadingDetails(true)
      const response = await apiService.previewUrlDetailed(url.trim())
      setDetailedPreview(response.data)
      toast.success('Detailed player stats loaded!')
    } catch (error) {
      console.error('Error loading detailed preview:', error)
      toast.error('Failed to load detailed stats. Please try again.')
    } finally {
      setLoadingDetails(false)
    }
  }

  const handleStoreGame = async () => {
    if (!preview) {
      toast.error('Please preview the URL first')
      return
    }

    try {
      setStoring(true)
      const response = await apiService.storeGame(url.trim(), selectedStats)

      if (response.data.success) {
        toast.success('Game stored successfully!')
        // Reset form
        setUrl('')
        setPreview(null)
        setSelectedStats({})
      } else {
        toast.info(response.data.message || 'Game already exists')
      }
    } catch (error) {
      console.error('Error storing game:', error)
      toast.error('Failed to store game. Please try again.')
    } finally {
      setStoring(false)
    }
  }

  const handleStatToggle = (category, stat) => {
    setSelectedStats(prev => ({
      ...prev,
      [category]: prev[category]
        ? prev[category].includes(stat)
          ? prev[category].filter(s => s !== stat)
          : [...prev[category], stat]
        : [stat]
    }))
  }

  const useRecommendedStats = () => {
    if (preview?.sport === 'mlb' && mlbRecommended) {
      setSelectedStats({
        player_stats: mlbRecommended.player_stats
      })
      toast.success('Applied recommended MLB stats')
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Add New Game</h1>
        <p className="text-gray-600 mt-1">
          Import game data from ESPN box score URLs
        </p>
      </div>

      {/* URL Input */}
      <div className="card p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">ESPN Box Score URL</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              ESPN URL
            </label>
            <div className="flex space-x-3">
              <input
                type="url"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                placeholder="https://www.espn.com/mlb/boxscore/_/gameId/401695669"
                className="input flex-1"
                disabled={loading}
              />
              <button
                onClick={handlePreview}
                disabled={loading || !url.trim()}
                className="btn btn-primary flex items-center space-x-2"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Link2 className="h-4 w-4" />
                )}
                <span>Preview</span>
              </button>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-2">
              <Info className="h-5 w-5 text-blue-500 mt-0.5" />
              <div className="text-sm text-blue-700">
                <p className="font-medium">Supported URLs:</p>
                <ul className="mt-1 space-y-1">
                  <li>• MLB: https://www.espn.com/mlb/boxscore/_/gameId/[ID]</li>
                  <li>• NHL: https://www.espn.com/nhl/boxscore/_/gameId/[ID]</li>
                  <li>• NBA: https://www.espn.com/nba/boxscore/_/gameId/[ID]</li>
                  <li>• NFL: https://www.espn.com/nfl/boxscore/_/gameId/[ID]</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Preview Results */}
      {preview && (
        <div className="card p-6">
          <div className="flex items-center space-x-2 mb-4">
            <CheckCircle className="h-5 w-5 text-success-500" />
            <h2 className="text-xl font-semibold text-gray-900">URL Preview</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Sport</label>
              <p className="text-lg font-semibold text-gray-900 uppercase">{preview.sport}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Game ID</label>
              <p className="text-lg font-semibold text-gray-900">{preview.game_id}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Status</label>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-success-500" />
                <span className="text-success-600 font-medium">Valid</span>
              </div>
            </div>
          </div>

          {/* Available Stats */}
          {preview.available_betting_stats && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Select Stats to Store</h3>
                {preview.sport === 'mlb' && (
                  <button
                    onClick={useRecommendedStats}
                    className="btn btn-secondary text-sm"
                  >
                    Use Recommended MLB Stats
                  </button>
                )}
              </div>

              {/* MLB Recommended Stats Info */}
              {preview.sport === 'mlb' && mlbRecommended && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div className="text-sm text-green-700">
                      <p className="font-medium">Recommended MLB Betting Stats:</p>
                      <p className="mt-1">{mlbRecommended.description}</p>
                      <div className="mt-2 flex flex-wrap gap-1">
                        {mlbRecommended.player_stats.map(stat => (
                          <span key={stat} className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
                            {stat.replace('_', ' ')}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Player Stats Selection */}
              {preview.available_betting_stats.player_stats && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Player Stats</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                    {preview.available_betting_stats.player_stats.map(stat => (
                      <label key={stat} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedStats.player_stats?.includes(stat) || false}
                          onChange={() => handleStatToggle('player_stats', stat)}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="text-sm text-gray-700">{stat.replace('_', ' ')}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}

              {/* Team Stats Selection */}
              {preview.available_betting_stats.team_stats && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Team Stats</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                    {preview.available_betting_stats.team_stats.map(stat => (
                      <label key={stat} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedStats.team_stats?.includes(stat) || false}
                          onChange={() => handleStatToggle('team_stats', stat)}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="text-sm text-gray-700">{stat.replace('_', ' ')}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Detailed Preview Button */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleDetailedPreview}
                disabled={loadingDetails}
                className="btn btn-secondary flex items-center space-x-2"
              >
                {loadingDetails ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Info className="h-4 w-4" />
                )}
                <span>{loadingDetails ? 'Loading...' : 'Show Player Stats Preview'}</span>
              </button>

              <button
                onClick={handleStoreGame}
                disabled={storing}
                className="btn btn-success flex items-center space-x-2"
              >
                {storing ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <CheckCircle className="h-4 w-4" />
                )}
                <span>{storing ? 'Storing...' : 'Store Game Data'}</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Detailed Player Stats Preview */}
      {detailedPreview && detailedPreview.player_stats_preview && (
        <div className="card p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Player Stats Preview</h2>
          <p className="text-gray-600 mb-6">
            Here are the actual player stats that will be stored in your database:
          </p>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {Object.entries(detailedPreview.player_stats_preview).map(([teamName, teamData]) => (
              <div key={teamName} className="border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{teamName}</h3>

                <div className="space-y-3">
                  {teamData.players?.map((player, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-3">
                      <h4 className="font-medium text-gray-900 mb-2">{player.name}</h4>

                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
                        {Object.entries(player.stats).map(([statName, statValue]) => (
                          <div key={statName} className="flex justify-between">
                            <span className="text-gray-600">{statName.replace('_', ' ')}:</span>
                            <span className="font-medium text-gray-900">{statValue}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}

                  {teamData.players?.length === 0 && (
                    <p className="text-gray-500 italic">No player stats available for this team</p>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Team Stats Preview */}
          {detailedPreview.team_stats_preview && Object.keys(detailedPreview.team_stats_preview).length > 0 && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Team Stats Preview</h3>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {Object.entries(detailedPreview.team_stats_preview).map(([teamName, teamStats]) => (
                  <div key={teamName} className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-3">{teamName}</h4>

                    <div className="grid grid-cols-2 gap-2 text-sm">
                      {Object.entries(teamStats).map(([statName, statValue]) => (
                        <div key={statName} className="flex justify-between">
                          <span className="text-gray-600">{statName.replace('_', ' ')}:</span>
                          <span className="font-medium text-gray-900">{statValue}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Error State */}
      {preview === false && (
        <div className="card p-6">
          <div className="flex items-center space-x-2 text-danger-600">
            <AlertCircle className="h-5 w-5" />
            <h2 className="text-xl font-semibold">Invalid URL</h2>
          </div>
          <p className="text-gray-600 mt-2">
            Please check the URL format and try again.
          </p>
        </div>
      )}
    </div>
  )
}

export default AddGame
