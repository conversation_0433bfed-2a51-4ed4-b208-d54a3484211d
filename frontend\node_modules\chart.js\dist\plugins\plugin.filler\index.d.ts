declare namespace _default {
    const id: string;
    function afterDatasetsUpdate(chart: any, _args: any, options: any): void;
    function beforeDraw(chart: any, _args: any, options: any): void;
    function beforeDatasetsDraw(chart: any, _args: any, options: any): void;
    function beforeDatasetDraw(chart: any, args: any, options: any): void;
    namespace defaults {
        const propagate: boolean;
        const drawTime: string;
    }
}
export default _default;
