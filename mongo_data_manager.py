"""
MongoDB-based Data Manager for Sports Betting Analysis
Handles manual URL input and selective stat storage using MongoDB
"""
from pymongo import MongoClient
from scrapers.espn_scraper import ESPNBoxScoreScraper
from datetime import datetime
import pandas as pd
from typing import List, Dict, Optional
import json
from bson import ObjectId

class MongoDataManager:
    def __init__(self, connection_string="mongodb://localhost:27017/", db_name="sports_betting"):
        """
        Initialize MongoDB connection
        For local MongoDB: mongodb://localhost:27017/
        For MongoDB Atlas: mongodb+srv://username:<EMAIL>/
        """
        self.client = MongoClient(connection_string)
        self.db = self.client[db_name]
        self.scraper = ESPNBoxScoreScraper()

        # Collections
        self.games = self.db.games
        self.players = self.db.players
        self.teams = self.db.teams
        self.player_stats = self.db.player_stats

        # Create indexes for better search performance
        self._create_indexes()

    def _create_indexes(self):
        """Create database indexes for better performance"""
        try:
            # Game indexes
            self.games.create_index("espn_game_id", unique=True)
            self.games.create_index("sport")
            self.games.create_index("date")

            # Player indexes
            self.players.create_index("name")
            self.players.create_index([("name", "text")])  # Text search

            # Team indexes
            self.teams.create_index("name")
            self.teams.create_index("abbreviation")
            self.teams.create_index([("name", "text"), ("abbreviation", "text")])

            # Player stats indexes
            self.player_stats.create_index("game_id")
            self.player_stats.create_index("player_id")
            self.player_stats.create_index("sport")

        except Exception as e:
            print(f"Note: Some indexes may already exist: {e}")

    def preview_url(self, url: str) -> Dict:
        """Preview what data can be extracted from a URL"""
        return self.scraper.preview_url_data(url)

    def preview_url_with_stats(self, url: str) -> Dict:
        """Preview URL with actual player stats data"""
        try:
            # First get basic preview
            basic_preview = self.scraper.preview_url_data(url)
            if 'error' in basic_preview:
                return basic_preview

            # Now scrape the actual data to show player stats
            scraped_data = self.scraper.scrape_boxscore_from_url(url)
            if 'error' in scraped_data:
                return scraped_data

            # Combine preview info with actual player data
            detailed_preview = {
                **basic_preview,
                'teams': {
                    'home_team': scraped_data.get('home_team', {}),
                    'away_team': scraped_data.get('away_team', {})
                },
                'player_stats_preview': self._format_player_stats_preview(scraped_data),
                'team_stats_preview': self._format_team_stats_preview(scraped_data)
            }

            return detailed_preview

        except Exception as e:
            return {'error': f'Failed to get detailed preview: {str(e)}'}

    def _format_player_stats_preview(self, scraped_data: Dict) -> Dict:
        """Format player stats for preview display"""
        preview = {}

        for team_type in ['home_team', 'away_team']:
            team_data = scraped_data.get(team_type, {})
            team_name = team_data.get('name', team_type)

            preview[team_name] = {
                'players': []
            }

            # Get player stats
            players = team_data.get('players', [])
            for player in players[:5]:  # Show first 5 players as preview
                player_preview = {
                    'name': player.get('name', 'Unknown'),
                    'stats': {}
                }

                # Extract key stats for preview
                stats = player.get('stats', {})
                for stat_name, stat_value in stats.items():
                    if stat_value is not None and stat_value != '':
                        player_preview['stats'][stat_name] = stat_value

                if player_preview['stats']:  # Only add if has stats
                    preview[team_name]['players'].append(player_preview)

        return preview

    def _format_team_stats_preview(self, scraped_data: Dict) -> Dict:
        """Format team stats for preview display"""
        preview = {}

        for team_type in ['home_team', 'away_team']:
            team_data = scraped_data.get(team_type, {})
            team_name = team_data.get('name', team_type)

            team_stats = team_data.get('team_stats', {})
            if team_stats:
                preview[team_name] = team_stats

        return preview

    def scrape_url_data(self, url: str) -> Dict:
        """Scrape data from ESPN URL"""
        return self.scraper.scrape_boxscore_from_url(url)

    def get_available_stats(self, sport: str) -> Dict:
        """Get available betting stats for a sport"""
        return self.scraper.get_available_stats(sport)

    def store_game_from_url(self, url: str, selected_stats: Dict = None) -> Dict:
        """
        Store game data from URL with selected stats
        selected_stats format: {
            'player_stats': ['hits', 'home_runs', 'rbis', 'strikeouts_pitcher', 'hits_allowed']
        }
        """
        # Scrape the data
        scraped_data = self.scrape_url_data(url)

        if 'error' in scraped_data:
            return {"error": scraped_data['error']}

        try:
            # Check if game already exists
            existing_game = self.games.find_one({"espn_game_id": scraped_data['game_id']})

            if existing_game:
                return {"message": "Game already exists", "game_id": str(existing_game['_id'])}

            # Create or get teams
            teams_data = scraped_data.get('teams', {})
            home_team = self._get_or_create_team(teams_data.get('home_team'), scraped_data['sport'])
            away_team = self._get_or_create_team(teams_data.get('away_team'), scraped_data['sport'])

            if not home_team or not away_team:
                return {"error": "Could not identify teams"}

            # Create game document
            game_info = scraped_data.get('game_info', {})
            final_score = game_info.get('final_score', [])

            game_doc = {
                "espn_game_id": scraped_data['game_id'],
                "url": url,
                "sport": scraped_data['sport'],
                "date": datetime.now(),  # You might want to parse actual date from game_info
                "home_team": {
                    "id": home_team['_id'],
                    "name": home_team['name']
                },
                "away_team": {
                    "id": away_team['_id'],
                    "name": away_team['name']
                },
                "home_score": final_score[1] if len(final_score) > 1 else None,
                "away_score": final_score[0] if len(final_score) > 0 else None,
                "status": "final",
                "venue": game_info.get('venue'),
                "created_at": datetime.now()
            }

            # Insert game
            game_result = self.games.insert_one(game_doc)
            game_id = game_result.inserted_id

            # Store selected player stats
            stats_stored = 0
            if scraped_data.get('player_stats'):
                stats_stored = self._store_selected_player_stats(
                    game_id, scraped_data['player_stats'],
                    selected_stats.get('player_stats', []) if selected_stats else None,
                    scraped_data['sport'], home_team, away_team
                )

            return {
                "success": True,
                "game_id": str(game_id),
                "sport": scraped_data['sport'],
                "teams": f"{away_team['name']} @ {home_team['name']}",
                "stats_stored": selected_stats or "all available",
                "player_stats_count": stats_stored
            }

        except Exception as e:
            return {"error": f"Database error: {str(e)}"}

    def _get_or_create_team(self, team_name: str, sport: str) -> Optional[Dict]:
        """Get existing team or create new one"""
        if not team_name:
            return None

        # Try to find existing team
        team = self.teams.find_one({"name": team_name, "sport": sport})

        if not team:
            # Create new team
            team_doc = {
                "name": team_name,
                "abbreviation": team_name[:3].upper(),  # Simple abbreviation
                "sport": sport,
                "created_at": datetime.now()
            }
            result = self.teams.insert_one(team_doc)
            team_doc['_id'] = result.inserted_id
            return team_doc

        return team

    def _store_selected_player_stats(self, game_id: ObjectId, player_stats: List[Dict],
                                   selected_stats: List[str], sport: str,
                                   home_team: Dict, away_team: Dict) -> int:
        """Store only selected player statistics"""
        stats_stored = 0

        for player_data in player_stats:
            player_name = player_data.get('player_name')
            if not player_name:
                continue

            # Get or create player
            player = self._get_or_create_player(player_name, sport)
            if not player:
                continue

            # Create player stat document with only selected stats
            stat_doc = {
                "game_id": game_id,
                "player_id": player['_id'],
                "player_name": player_name,
                "sport": sport,
                "created_at": datetime.now()
            }

            # Add selected stats or all stats if none selected
            stats_to_store = selected_stats if selected_stats else list(player_data.keys())

            for stat_name in stats_to_store:
                if stat_name in player_data and stat_name != 'player_name':
                    stat_doc[stat_name] = player_data[stat_name]

            # Only store if we have actual stats (more than just metadata)
            if len(stat_doc) > 5:  # More than just game_id, player_id, player_name, sport, created_at
                self.player_stats.insert_one(stat_doc)
                stats_stored += 1

        return stats_stored

    def _get_or_create_player(self, player_name: str, sport: str) -> Optional[Dict]:
        """Get existing player or create new one"""
        if not player_name:
            return None

        # Try to find existing player
        player = self.players.find_one({"name": player_name})

        if not player:
            # Create new player
            player_doc = {
                "name": player_name,
                "sport": sport,
                "created_at": datetime.now()
            }
            result = self.players.insert_one(player_doc)
            player_doc['_id'] = result.inserted_id
            return player_doc

        return player

    def search_players(self, query: str, sport: str = None, limit: int = 20) -> List[Dict]:
        """Search for players by name"""
        try:
            # Build search filter
            search_filter = {"$text": {"$search": query}}
            if sport:
                search_filter["sport"] = sport

            # Find players
            players_cursor = self.players.find(search_filter).limit(limit)

            results = []
            for player in players_cursor:
                # Get some recent stats for this player
                recent_stats = self.player_stats.find(
                    {"player_id": player['_id']}
                ).sort("created_at", -1).limit(1)

                recent_stat = list(recent_stats)

                results.append({
                    'id': str(player['_id']),
                    'name': player['name'],
                    'sport': player.get('sport', 'Unknown'),
                    'recent_game': recent_stat[0].get('created_at') if recent_stat else None
                })

            return results
        except Exception as e:
            print(f"Search error: {e}")
            return []

    def search_teams(self, query: str, sport: str = None, limit: int = 20) -> List[Dict]:
        """Search for teams by name or abbreviation"""
        try:
            # Build search filter
            search_filter = {"$text": {"$search": query}}
            if sport:
                search_filter["sport"] = sport

            # Find teams
            teams_cursor = self.teams.find(search_filter).limit(limit)

            results = []
            for team in teams_cursor:
                # Count games for this team
                game_count = self.games.count_documents({
                    "$or": [
                        {"home_team.id": team['_id']},
                        {"away_team.id": team['_id']}
                    ]
                })

                results.append({
                    'id': str(team['_id']),
                    'name': team['name'],
                    'abbreviation': team.get('abbreviation', ''),
                    'sport': team.get('sport', 'Unknown'),
                    'games_count': game_count
                })

            return results
        except Exception as e:
            print(f"Search error: {e}")
            return []

    def get_player_stats_summary(self, player_id: str, stat_types: List[str] = None) -> Dict:
        """Get statistical summary for a player"""
        try:
            player = self.players.find_one({"_id": ObjectId(player_id)})
            if not player:
                return {"error": "Player not found"}

            # Get all stats for this player
            stats_cursor = self.player_stats.find({"player_id": ObjectId(player_id)})
            stats = list(stats_cursor)

            if not stats:
                return {"error": "No stats found for player"}

            # Calculate averages and totals
            summary = {
                'player_name': player['name'],
                'games_played': len(stats),
                'sport': player.get('sport'),
                'stats': {}
            }

            # Define which stats to summarize
            if not stat_types:
                # Get all numeric stat fields from the first record
                first_stat = stats[0]
                stat_types = [key for key, value in first_stat.items()
                            if isinstance(value, (int, float)) and key not in ['game_id', 'player_id']]

            for stat_type in stat_types:
                values = []
                for stat in stats:
                    value = stat.get(stat_type)
                    if value is not None and isinstance(value, (int, float)):
                        values.append(float(value))

                if values:
                    summary['stats'][stat_type] = {
                        'total': sum(values),
                        'average': sum(values) / len(values),
                        'max': max(values),
                        'min': min(values),
                        'games_with_stat': len(values)
                    }

            return summary
        except Exception as e:
            return {"error": f"Database error: {str(e)}"}

    def list_stored_games(self, sport: str = None, limit: int = 50) -> List[Dict]:
        """List all stored games"""
        try:
            # Build filter
            game_filter = {}
            if sport:
                game_filter["sport"] = sport

            # Find games
            games_cursor = self.games.find(game_filter).sort("created_at", -1).limit(limit)

            results = []
            for game in games_cursor:
                results.append({
                    'id': str(game['_id']),
                    'espn_id': game['espn_game_id'],
                    'date': game.get('date'),
                    'home_team': game['home_team']['name'],
                    'away_team': game['away_team']['name'],
                    'score': f"{game.get('away_score', '?')}-{game.get('home_score', '?')}",
                    'sport': game['sport'],
                    'url': game.get('url')
                })

            return results
        except Exception as e:
            return []

    def get_database_stats(self) -> Dict:
        """Get overall database statistics"""
        try:
            return {
                'total_games': self.games.count_documents({}),
                'total_players': self.players.count_documents({}),
                'total_teams': self.teams.count_documents({}),
                'total_player_stats': self.player_stats.count_documents({}),
                'sports': list(self.games.distinct('sport')),
                'connection_status': 'connected'
            }
        except Exception as e:
            return {'connection_status': 'error', 'error': str(e)}

    def get_team_averages(self, team_name: str, sport: str, last_n_games: int = 10) -> Dict:
        """Get team's average stats from recent games"""
        try:
            # Find the team
            team = self.teams.find_one({
                "$or": [
                    {"name": {"$regex": team_name, "$options": "i"}},
                    {"abbreviation": {"$regex": team_name, "$options": "i"}}
                ],
                "sport": sport
            })

            if not team:
                return {"error": f"Team '{team_name}' not found in {sport}"}

            # Get recent games for this team
            games = list(self.games.find({
                "$or": [
                    {"home_team.name": team["name"]},
                    {"away_team.name": team["name"]}
                ],
                "sport": sport
            }).sort("date", -1).limit(last_n_games))

            if not games:
                return {"error": f"No recent games found for {team_name}"}

            # Calculate averages from player stats with home/away breakdown
            team_stats = {
                "team_name": team["name"],
                "games_analyzed": len(games),
                "player_averages": {},
                "home_averages": {},
                "away_averages": {},
                "recent_performance": [],
                "home_away_record": {"home_games": 0, "away_games": 0, "home_wins": 0, "away_wins": 0}
            }

            # Separate stats by home/away
            all_stats = {}
            home_stats = {}
            away_stats = {}

            for game in games:
                is_home = game["home_team"]["name"] == team["name"]
                game_stats = list(self.player_stats.find({"game_id": game["_id"]}))

                # Track wins/losses by home/away
                if game.get("home_score") is not None and game.get("away_score") is not None:
                    if is_home:
                        team_stats["home_away_record"]["home_games"] += 1
                        if game["home_score"] > game["away_score"]:
                            team_stats["home_away_record"]["home_wins"] += 1
                    else:
                        team_stats["home_away_record"]["away_games"] += 1
                        if game["away_score"] > game["home_score"]:
                            team_stats["home_away_record"]["away_wins"] += 1

                # Add game performance summary
                game_summary = {
                    "date": game.get("date"),
                    "opponent": game["away_team"]["name"] if is_home else game["home_team"]["name"],
                    "home_away": "home" if is_home else "away",
                    "score": f"{game.get('home_score', 0)}-{game.get('away_score', 0)}" if game.get("home_score") is not None else "N/A"
                }
                team_stats["recent_performance"].append(game_summary)

                # Aggregate stats
                for stat in game_stats:
                    for key, value in stat.items():
                        if key not in ["_id", "game_id", "player_id"] and value is not None:
                            try:
                                float_value = float(value)

                                # Overall stats
                                if key not in all_stats:
                                    all_stats[key] = []
                                all_stats[key].append(float_value)

                                # Home/Away specific stats
                                if is_home:
                                    if key not in home_stats:
                                        home_stats[key] = []
                                    home_stats[key].append(float_value)
                                else:
                                    if key not in away_stats:
                                        away_stats[key] = []
                                    away_stats[key].append(float_value)

                            except (ValueError, TypeError):
                                continue

            # Calculate overall averages
            for stat_name, values in all_stats.items():
                if values:
                    team_stats["player_averages"][stat_name] = {
                        "average": sum(values) / len(values),
                        "total_instances": len(values),
                        "max": max(values),
                        "min": min(values)
                    }

            # Calculate home averages
            for stat_name, values in home_stats.items():
                if values:
                    team_stats["home_averages"][stat_name] = {
                        "average": sum(values) / len(values),
                        "total_instances": len(values)
                    }

            # Calculate away averages
            for stat_name, values in away_stats.items():
                if values:
                    team_stats["away_averages"][stat_name] = {
                        "average": sum(values) / len(values),
                        "total_instances": len(values)
                    }

            # Calculate win rates
            if team_stats["home_away_record"]["home_games"] > 0:
                team_stats["home_away_record"]["home_win_rate"] = team_stats["home_away_record"]["home_wins"] / team_stats["home_away_record"]["home_games"]
            if team_stats["home_away_record"]["away_games"] > 0:
                team_stats["home_away_record"]["away_win_rate"] = team_stats["home_away_record"]["away_wins"] / team_stats["home_away_record"]["away_games"]

            return team_stats

        except Exception as e:
            return {"error": f"Failed to get team averages: {str(e)}"}

    def predict_game_outcome(self, team1_name: str, team2_name: str, sport: str) -> Dict:
        """Predict game outcome between two teams based on historical data"""
        try:
            # Get averages for both teams
            team1_stats = self.get_team_averages(team1_name, sport, 10)
            team2_stats = self.get_team_averages(team2_name, sport, 10)

            if "error" in team1_stats:
                return {"error": f"Team 1 error: {team1_stats['error']}"}
            if "error" in team2_stats:
                return {"error": f"Team 2 error: {team2_stats['error']}"}

            # Simple prediction based on key stats
            prediction = {
                "team1": {
                    "name": team1_stats["team_name"],
                    "games_analyzed": team1_stats["games_analyzed"],
                    "key_stats": self._extract_key_stats(team1_stats["player_averages"], sport)
                },
                "team2": {
                    "name": team2_stats["team_name"],
                    "games_analyzed": team2_stats["games_analyzed"],
                    "key_stats": self._extract_key_stats(team2_stats["player_averages"], sport)
                },
                "predictions": self._generate_predictions(team1_stats, team2_stats, sport),
                "confidence": "medium",  # Simple confidence rating
                "note": "Predictions based on recent performance averages"
            }

            return prediction

        except Exception as e:
            return {"error": f"Failed to predict game outcome: {str(e)}"}

    def _extract_key_stats(self, player_averages: Dict, sport: str) -> Dict:
        """Extract key stats for prediction based on sport"""
        key_stats = {}

        if sport == "mlb":
            mlb_keys = ["hits", "home_runs", "rbis", "strikeouts_pitcher", "hits_allowed", "runs", "earned_runs"]
            for key in mlb_keys:
                if key in player_averages:
                    key_stats[key] = player_averages[key]["average"]
        elif sport == "nhl":
            nhl_keys = ["goals", "assists", "shots", "saves", "goals_allowed"]
            for key in nhl_keys:
                if key in player_averages:
                    key_stats[key] = player_averages[key]["average"]
        elif sport == "nba":
            nba_keys = ["points", "rebounds", "assists", "steals", "blocks", "turnovers", "plus_minus"]
            for key in nba_keys:
                if key in player_averages:
                    key_stats[key] = player_averages[key]["average"]

        return key_stats

    def _generate_predictions(self, team1_stats: Dict, team2_stats: Dict, sport: str) -> Dict:
        """Generate simple predictions based on team stats"""
        predictions = {}

        team1_key = team1_stats["player_averages"]
        team2_key = team2_stats["player_averages"]

        if sport == "mlb":
            # Predict runs scored
            team1_offensive = self._calculate_offensive_score(team1_key, ["hits", "home_runs", "rbis"])
            team2_offensive = self._calculate_offensive_score(team2_key, ["hits", "home_runs", "rbis"])

            predictions["predicted_runs"] = {
                team1_stats["team_name"]: round(team1_offensive * 5, 1),  # Simple scaling
                team2_stats["team_name"]: round(team2_offensive * 5, 1)
            }

            # Predict key player performances
            predictions["player_predictions"] = {
                "hits_over_under": self._predict_stat_total(team1_key, team2_key, "hits"),
                "home_runs_total": self._predict_stat_total(team1_key, team2_key, "home_runs"),
                "strikeouts_total": self._predict_stat_total(team1_key, team2_key, "strikeouts_pitcher")
            }

        elif sport == "nba":
            # Predict total points
            team1_offensive = self._calculate_offensive_score(team1_key, ["points", "field_goals_made", "three_pointers_made"])
            team2_offensive = self._calculate_offensive_score(team2_key, ["points", "field_goals_made", "three_pointers_made"])

            predictions["predicted_points"] = {
                team1_stats["team_name"]: round(team1_offensive * 35, 1),  # NBA scaling
                team2_stats["team_name"]: round(team2_offensive * 35, 1)
            }

            # Predict key NBA performances
            predictions["player_predictions"] = {
                "total_points": self._predict_stat_total(team1_key, team2_key, "points"),
                "total_rebounds": self._predict_stat_total(team1_key, team2_key, "rebounds"),
                "total_assists": self._predict_stat_total(team1_key, team2_key, "assists"),
                "total_steals": self._predict_stat_total(team1_key, team2_key, "steals"),
                "total_blocks": self._predict_stat_total(team1_key, team2_key, "blocks"),
                "total_turnovers": self._predict_stat_total(team1_key, team2_key, "turnovers")
            }

            # Defensive analysis using plus_minus
            team1_defense = team1_key.get("plus_minus", {}).get("average", 0)
            team2_defense = team2_key.get("plus_minus", {}).get("average", 0)

            predictions["defensive_analysis"] = {
                "team1_defensive_rating": "strong" if team1_defense > 2 else "average" if team1_defense > -2 else "weak",
                "team2_defensive_rating": "strong" if team2_defense > 2 else "average" if team2_defense > -2 else "weak",
                "predicted_pace": "fast" if (team1_offensive + team2_offensive) > 220 else "slow"
            }

        return predictions

    def _calculate_offensive_score(self, stats: Dict, offensive_stats: List[str]) -> float:
        """Calculate a simple offensive score"""
        score = 0
        count = 0
        for stat in offensive_stats:
            if stat in stats:
                score += stats[stat]["average"]
                count += 1
        return score / count if count > 0 else 0

    def _predict_stat_total(self, team1_stats: Dict, team2_stats: Dict, stat_name: str) -> Dict:
        """Predict total for a specific stat"""
        team1_avg = team1_stats.get(stat_name, {}).get("average", 0)
        team2_avg = team2_stats.get(stat_name, {}).get("average", 0)

        total = team1_avg + team2_avg
        return {
            "predicted_total": round(total, 1),
            "team1_contribution": round(team1_avg, 1),
            "team2_contribution": round(team2_avg, 1)
        }

    def close_connection(self):
        """Close MongoDB connection"""
        self.client.close()
