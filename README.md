# 🏆 Sports Betting Analysis System

A comprehensive system for ESPN box score analysis with selective stat storage for sports betting insights. Upload games, track player performance, and make informed betting decisions!

## 🚀 **Quick Start - Get Running Today!**

### **Option 1: Web App (Recommended)**
1. **Start Backend**: Double-click `start_backend.bat`
2. **Start Frontend**: Double-click `setup_frontend.bat`
3. **Open Browser**: Go to http://localhost:3000
4. **Upload Games**: Paste ESPN URLs and start analyzing!

### **Option 2: Command Line**
1. **Install**: `pip install -r requirements.txt`
2. **Start MongoDB**: Make sure MongoDB is running locally
3. **Upload Game**: `python mongo_main.py --store "YOUR_ESPN_URL"`
4. **Search Players**: `python mongo_main.py --search-players "Player Name"`

## 🎯 **What This System Does**

- **📥 Upload Games**: Paste ESPN box score URLs to instantly import game data
- **🎲 Track Betting Stats**: Focus on stats that matter for betting (strikeouts, hits, RBIs, etc.)
- **🔍 Search & Analyze**: Find player trends and team performance patterns
- **📊 Make Picks**: Use historical data to inform your betting decisions
- **💾 Build Database**: Accumulate data over time for better analysis

## 📋 **Step-by-Step Instructions**

### **🌐 Web App Method (Easiest)**

1. **Prerequisites**:
   - Make sure MongoDB is running locally (download from mongodb.com)
   - Have Python 3.8+ and Node.js 16+ installed

2. **Start the System**:
   ```bash
   # Terminal 1: Start Backend
   start_backend.bat

   # Terminal 2: Start Frontend
   setup_frontend.bat
   ```

3. **Use the Web Interface**:
   - Open http://localhost:3000 in your browser
   - Click "Add Game" in the sidebar
   - Paste an ESPN box score URL (like: `https://www.espn.com/mlb/boxscore/_/gameId/401695669`)
   - Preview the data and select which stats to store
   - Click "Store Game" to save to your database

4. **Analyze Your Data**:
   - Use "Players" tab to search for specific players
   - Use "Teams" tab to find team information
   - Use "Games" tab to see all stored games
   - Build your database over time for better analysis

### **💻 Command Line Method**

1. **Setup**:
   ```bash
   pip install -r requirements.txt
   # Make sure MongoDB is running locally
   ```

2. **Upload Your First Game**:
   ```bash
   # Preview what data is available
   python mongo_main.py --preview "https://www.espn.com/mlb/boxscore/_/gameId/401695669"

   # Store the game data
   python mongo_main.py --store "https://www.espn.com/mlb/boxscore/_/gameId/401695669"
   ```

3. **Interactive Mode** (Recommended):
   ```bash
   python mongo_main.py --interactive

   # Then use commands like:
   # preview <URL>
   # store <URL>
   # search-players <name>
   # search-teams <name>
   # quit
   ```

## 🎲 **Your MLB Betting Stats (Configured)**

Based on your preferences, the system tracks these key MLB betting stats:

### **🥎 Pitcher Stats**
- **Strikeouts** (`strikeouts_pitcher`) - Key for strikeout props
- **Hits Allowed** (`hits_allowed`) - Important for pitcher performance

### **⚾ Batter Stats**
- **Hits** (`hits`) - Essential for hit props
- **Home Runs** (`home_runs`) - Critical for HR betting
- **RBIs** (`rbis`) - Important for RBI props

### **📊 All Available Stats by Sport**

<details>
<summary><strong>MLB (Baseball)</strong></summary>

**Player Stats**: hits, runs, rbis, home_runs, stolen_bases, strikeouts_batter, walks_batter, innings_pitched, strikeouts_pitcher, walks_pitcher, earned_runs, hits_allowed
**Team Stats**: runs, hits, errors, left_on_base
</details>

<details>
<summary><strong>NHL (Hockey)</strong></summary>

**Player Stats**: goals, assists, points, shots, hits, blocked_shots, penalty_minutes, plus_minus, faceoff_wins, faceoff_attempts, time_on_ice
**Goalie Stats**: saves, shots_against, goals_allowed, save_percentage, time_on_ice
**Team Stats**: goals, shots, hits, blocked_shots, faceoff_percentage, penalty_minutes, power_play_goals, power_play_opportunities
</details>

<details>
<summary><strong>NBA (Basketball)</strong></summary>

**Player Stats**: points, rebounds, assists, steals, blocks, turnovers, field_goals_made, field_goals_attempted, three_pointers_made, three_pointers_attempted, free_throws_made, free_throws_attempted, minutes
**Team Stats**: points, field_goal_percentage, three_point_percentage, free_throw_percentage, rebounds, assists, turnovers, steals, blocks
</details>

<details>
<summary><strong>NFL (Football)</strong></summary>

**Player Stats**: passing_yards, passing_touchdowns, interceptions, rushing_yards, rushing_touchdowns, receiving_yards, receiving_touchdowns, receptions, tackles, sacks
**Team Stats**: total_yards, passing_yards, rushing_yards, turnovers, penalties, time_of_possession
</details>

## 🔥 **Example Workflow - Upload Games Today**

### **Getting Started Right Now**

1. **Start the Web App**:
   ```bash
   # Open two terminals and run:
   start_backend.bat
   setup_frontend.bat
   ```

2. **Find ESPN Box Score URLs**:
   - Go to ESPN.com
   - Navigate to MLB > Scores
   - Click on any completed game
   - Copy the URL (should look like: `https://www.espn.com/mlb/boxscore/_/gameId/XXXXXXX`)

3. **Upload Your First Game**:
   - Open http://localhost:3000
   - Click "Add Game"
   - Paste the ESPN URL
   - Preview the data
   - Select your preferred stats (or use the MLB defaults)
   - Click "Store Game"

4. **Start Building Your Database**:
   - Upload 5-10 recent games
   - Search for players you're interested in betting on
   - Look for patterns in their performance
   - Use this data to inform your picks

### **Command Line Quick Start**

```bash
# Install and setup
pip install -r requirements.txt

# Upload a game (replace with real ESPN URL)
python mongo_main.py --store "https://www.espn.com/mlb/boxscore/_/gameId/401695669"

# Search for a player
python mongo_main.py --search-players "Ohtani"

# Interactive mode for easier use
python mongo_main.py --interactive
```

### **Finding ESPN URLs**

**MLB**: `https://www.espn.com/mlb/boxscore/_/gameId/XXXXXXX`
**NHL**: `https://www.espn.com/nhl/boxscore/_/gameId/XXXXXXX`
**NBA**: `https://www.espn.com/nba/boxscore/_/gameId/XXXXXXX`
**NFL**: `https://www.espn.com/nfl/boxscore/_/gameId/XXXXXXX`

## 🎯 **Making Betting Picks**

### **How to Use This System for Betting**

1. **Build Your Database**:
   - Upload 10-20 recent games for teams/players you're interested in
   - Focus on the stats you bet on most (strikeouts, hits, RBIs, etc.)

2. **Analyze Player Trends**:
   ```bash
   # Search for a player
   python mongo_main.py --search-players "Shohei Ohtani"

   # View their stats across games
   python mongo_main.py --interactive
   # Then: player-stats <PLAYER_ID>
   ```

3. **Look for Patterns**:
   - **Hot Streaks**: Player hitting over their average recently
   - **Matchup Analysis**: How they perform against specific teams
   - **Consistency**: Players with reliable performance
   - **Venue Effects**: Home vs away performance differences

4. **Make Informed Decisions**:
   - Use historical data to spot value bets
   - Identify players due for regression
   - Find consistent performers for safer bets

### **Key Betting Stats to Track**

**For Strikeout Props**: Track `strikeouts_pitcher` across recent games
**For Hit Props**: Monitor `hits` patterns for batters
**For RBI Props**: Analyze `rbis` trends and matchups
**For HR Props**: Watch `home_runs` frequency and ballpark factors

## 🛠 **System Architecture**

### **Database**: MongoDB (Local)
- **Games**: Game results and metadata
- **Players**: Player details across all sports
- **Teams**: Team information and abbreviations
- **PlayerStats**: Individual player statistics per game

### **Available Interfaces**:
1. **Web App**: Modern React frontend (http://localhost:3000)
2. **API**: FastAPI backend (http://localhost:8000)
3. **Command Line**: Direct Python scripts

### **Data Flow**:
ESPN URL → Scraper → Database → Analysis → Betting Insights

## 🚨 **Troubleshooting**

### **Common Issues & Solutions**

1. **"MongoDB Connection Error"**:
   - Download and install MongoDB from mongodb.com
   - Make sure MongoDB service is running
   - Default connection: `mongodb://localhost:27017/`

2. **"Module not found" errors**:
   ```bash
   pip install -r requirements.txt
   pip install -r api/requirements.txt  # For web app
   ```

3. **"ESPN scraping failed"**:
   - Check if the ESPN URL is valid and complete
   - ESPN may have changed their HTML structure
   - Try a different recent game URL

4. **Web app won't start**:
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

5. **No data showing up**:
   - Make sure you've uploaded games first
   - Check that MongoDB is running
   - Verify the game was stored successfully

### **Getting Help**

- Check error messages carefully
- Ensure all prerequisites are installed
- Try the command line version if web app fails
- Make sure ESPN URLs are from completed games

## 📈 **Next Steps & Advanced Features**

### **Immediate Improvements**
- Upload more historical games for better analysis
- Track specific players you bet on regularly
- Build custom queries for your betting patterns

### **Future Enhancements**
- Real-time odds integration
- Machine learning prediction models
- Advanced statistical analysis
- Mobile app version
- Automated game importing

---

## ⚖️ **Important Disclaimers**

- **Educational Purpose**: This system is for analysis and educational purposes only
- **Bet Responsibly**: Always gamble responsibly and within your means
- **Legal Compliance**: Ensure sports betting is legal in your jurisdiction
- **No Guarantees**: Past performance does not guarantee future results

---

## 🎉 **You're Ready to Go!**

Your sports betting analysis system is ready! Start by:

1. **Running**: `start_backend.bat` and `setup_frontend.bat`
2. **Uploading**: 5-10 recent MLB games
3. **Analyzing**: Search for players you're interested in betting on
4. **Building**: Your database over time for better insights

**Good luck with your picks! 🍀**
