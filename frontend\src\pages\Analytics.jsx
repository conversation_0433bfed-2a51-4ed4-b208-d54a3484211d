import React, { useState } from 'react'
import { BarChart3, TrendingUp, Target, AlertCircle, Zap, Loader2, Users, Trophy } from 'lucide-react'
import { apiService } from '../services/api'
import toast from 'react-hot-toast'

const Analytics = () => {
  const [team1, setTeam1] = useState('')
  const [team2, setTeam2] = useState('')
  const [sport, setSport] = useState('mlb')
  const [prediction, setPrediction] = useState(null)
  const [loading, setLoading] = useState(false)

  const handlePredict = async () => {
    if (!team1.trim() || !team2.trim()) {
      toast.error('Please enter both team names')
      return
    }

    try {
      setLoading(true)
      const response = await apiService.predictGame(team1.trim(), team2.trim(), sport)

      if (response.data.error) {
        toast.error(response.data.error)
        setPrediction(null)
      } else {
        setPrediction(response.data)
        toast.success('Game prediction generated!')
      }
    } catch (error) {
      console.error('Error predicting game:', error)
      toast.error('Failed to generate prediction. Make sure both teams have game data.')
      setPrediction(null)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
        <p className="text-gray-600 mt-1">Advanced statistical analysis and game predictions</p>
      </div>

      {/* Game Predictor */}
      <div className="card p-6">
        <div className="flex items-center space-x-2 mb-6">
          <Zap className="h-6 w-6 text-blue-500" />
          <h2 className="text-2xl font-bold text-gray-900">Game Predictor</h2>
        </div>

        <p className="text-gray-600 mb-6">
          Select two teams to predict game outcomes and player stats based on historical data from your database.
        </p>

        {/* Team Selection */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Sport</label>
            <select
              value={sport}
              onChange={(e) => setSport(e.target.value)}
              className="input"
            >
              <option value="mlb">MLB</option>
              <option value="nhl">NHL</option>
              <option value="nba">NBA</option>
              <option value="nfl">NFL</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Team 1</label>
            <input
              type="text"
              value={team1}
              onChange={(e) => setTeam1(e.target.value)}
              placeholder="Enter team name..."
              className="input"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Team 2</label>
            <input
              type="text"
              value={team2}
              onChange={(e) => setTeam2(e.target.value)}
              placeholder="Enter team name..."
              className="input"
            />
          </div>

          <div className="flex items-end">
            <button
              onClick={handlePredict}
              disabled={loading}
              className="btn btn-primary w-full flex items-center justify-center space-x-2"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Trophy className="h-4 w-4" />
              )}
              <span>{loading ? 'Predicting...' : 'Predict Game'}</span>
            </button>
          </div>
        </div>

        {/* Example Teams */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <p className="text-sm text-blue-700">
            <strong>Example team names:</strong> "Yankees", "Dodgers", "Red Sox", "Astros" (or any team name/abbreviation from your database)
          </p>
        </div>
      </div>

      {/* Prediction Results */}
      {prediction && (
        <div className="card p-6">
          <div className="flex items-center space-x-2 mb-6">
            <Target className="h-6 w-6 text-green-500" />
            <h2 className="text-2xl font-bold text-gray-900">Prediction Results</h2>
          </div>

          {/* Team Comparison */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Team 1 */}
            <div className="border border-gray-200 rounded-lg p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Users className="h-5 w-5 text-blue-500" />
                <h3 className="text-xl font-semibold text-gray-900">{prediction.team1.name}</h3>
              </div>

              <p className="text-sm text-gray-600 mb-4">
                Based on {prediction.team1.games_analyzed} recent games
              </p>

              {/* Home/Away Record */}
              {prediction.team1.home_away_record && (
                <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                  <h5 className="font-medium text-blue-900 mb-2">Home/Away Record</h5>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-blue-700">Home: </span>
                      <span className="font-medium">
                        {prediction.team1.home_away_record.home_wins || 0}-{(prediction.team1.home_away_record.home_games || 0) - (prediction.team1.home_away_record.home_wins || 0)}
                        {prediction.team1.home_away_record.home_win_rate &&
                          ` (${(prediction.team1.home_away_record.home_win_rate * 100).toFixed(0)}%)`
                        }
                      </span>
                    </div>
                    <div>
                      <span className="text-blue-700">Away: </span>
                      <span className="font-medium">
                        {prediction.team1.home_away_record.away_wins || 0}-{(prediction.team1.home_away_record.away_games || 0) - (prediction.team1.home_away_record.away_wins || 0)}
                        {prediction.team1.home_away_record.away_win_rate &&
                          ` (${(prediction.team1.home_away_record.away_win_rate * 100).toFixed(0)}%)`
                        }
                      </span>
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">Key Stats (Averages)</h4>
                {Object.entries(prediction.team1.key_stats).map(([stat, value]) => (
                  <div key={stat} className="flex justify-between text-sm">
                    <span className="text-gray-600">{stat.replace('_', ' ')}:</span>
                    <span className="font-medium text-gray-900">{value.toFixed(2)}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Team 2 */}
            <div className="border border-gray-200 rounded-lg p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Users className="h-5 w-5 text-red-500" />
                <h3 className="text-xl font-semibold text-gray-900">{prediction.team2.name}</h3>
              </div>

              <p className="text-sm text-gray-600 mb-4">
                Based on {prediction.team2.games_analyzed} recent games
              </p>

              {/* Home/Away Record */}
              {prediction.team2.home_away_record && (
                <div className="mb-4 p-3 bg-red-50 rounded-lg">
                  <h5 className="font-medium text-red-900 mb-2">Home/Away Record</h5>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-red-700">Home: </span>
                      <span className="font-medium">
                        {prediction.team2.home_away_record.home_wins || 0}-{(prediction.team2.home_away_record.home_games || 0) - (prediction.team2.home_away_record.home_wins || 0)}
                        {prediction.team2.home_away_record.home_win_rate &&
                          ` (${(prediction.team2.home_away_record.home_win_rate * 100).toFixed(0)}%)`
                        }
                      </span>
                    </div>
                    <div>
                      <span className="text-red-700">Away: </span>
                      <span className="font-medium">
                        {prediction.team2.home_away_record.away_wins || 0}-{(prediction.team2.home_away_record.away_games || 0) - (prediction.team2.home_away_record.away_wins || 0)}
                        {prediction.team2.home_away_record.away_win_rate &&
                          ` (${(prediction.team2.home_away_record.away_win_rate * 100).toFixed(0)}%)`
                        }
                      </span>
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">Key Stats (Averages)</h4>
                {Object.entries(prediction.team2.key_stats).map(([stat, value]) => (
                  <div key={stat} className="flex justify-between text-sm">
                    <span className="text-gray-600">{stat.replace('_', ' ')}:</span>
                    <span className="font-medium text-gray-900">{value.toFixed(2)}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Predictions */}
          {prediction.predictions && (
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Game Predictions</h3>

              {/* Predicted Runs (MLB) */}
              {prediction.predictions.predicted_runs && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                  <h4 className="font-medium text-green-900 mb-2">Predicted Runs</h4>
                  <div className="grid grid-cols-2 gap-4">
                    {Object.entries(prediction.predictions.predicted_runs).map(([team, runs]) => (
                      <div key={team} className="text-center">
                        <p className="text-sm text-green-700">{team}</p>
                        <p className="text-2xl font-bold text-green-900">{runs}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Predicted Points (NBA) */}
              {prediction.predictions.predicted_points && (
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
                  <h4 className="font-medium text-orange-900 mb-2">Predicted Points</h4>
                  <div className="grid grid-cols-2 gap-4">
                    {Object.entries(prediction.predictions.predicted_points).map(([team, points]) => (
                      <div key={team} className="text-center">
                        <p className="text-sm text-orange-700">{team}</p>
                        <p className="text-2xl font-bold text-orange-900">{points}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* NBA Defensive Analysis */}
              {prediction.predictions.defensive_analysis && (
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-4">
                  <h4 className="font-medium text-purple-900 mb-2">Defensive Analysis</h4>
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <p className="text-sm text-purple-700">{prediction.team1.name} Defense</p>
                      <p className="text-lg font-bold text-purple-900 capitalize">
                        {prediction.predictions.defensive_analysis.team1_defensive_rating}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-purple-700">Game Pace</p>
                      <p className="text-lg font-bold text-purple-900 capitalize">
                        {prediction.predictions.defensive_analysis.predicted_pace}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-purple-700">{prediction.team2.name} Defense</p>
                      <p className="text-lg font-bold text-purple-900 capitalize">
                        {prediction.predictions.defensive_analysis.team2_defensive_rating}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Player Predictions */}
              {prediction.predictions.player_predictions && (
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Player Stat Predictions</h4>

                  {Object.entries(prediction.predictions.player_predictions).map(([statName, statData]) => (
                    <div key={statName} className="bg-gray-50 rounded-lg p-4">
                      <h5 className="font-medium text-gray-900 mb-2">
                        {statName.replace('_', ' ').toUpperCase()}
                      </h5>

                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div className="text-center">
                          <p className="text-gray-600">Total Predicted</p>
                          <p className="text-lg font-semibold text-gray-900">
                            {statData.predicted_total}
                          </p>
                        </div>
                        <div className="text-center">
                          <p className="text-gray-600">{prediction.team1.name}</p>
                          <p className="text-lg font-semibold text-blue-600">
                            {statData.team1_contribution}
                          </p>
                        </div>
                        <div className="text-center">
                          <p className="text-gray-600">{prediction.team2.name}</p>
                          <p className="text-lg font-semibold text-red-600">
                            {statData.team2_contribution}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Disclaimer */}
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center space-x-2 text-yellow-700">
              <AlertCircle className="h-5 w-5" />
              <p className="font-medium">Prediction Disclaimer</p>
            </div>
            <p className="text-sm text-yellow-600 mt-1">
              {prediction.note} Confidence: {prediction.confidence}. Use for analysis only - past performance doesn't guarantee future results.
            </p>
          </div>
        </div>
      )}

      {/* Future Features */}
      <div className="card p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Coming Soon</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <TrendingUp className="h-8 w-8 text-blue-500 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">Performance Trends</h3>
            <p className="text-sm text-gray-600">
              Track player and team performance over time with interactive charts
            </p>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <Target className="h-8 w-8 text-green-500 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">Betting Insights</h3>
            <p className="text-sm text-gray-600">
              Advanced betting opportunity identification based on statistical patterns
            </p>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <BarChart3 className="h-8 w-8 text-purple-500 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">Advanced Analytics</h3>
            <p className="text-sm text-gray-600">
              Machine learning models and comparative analysis tools
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Analytics
