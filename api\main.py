"""
FastAPI Backend for Sports Betting Analysis System
Exposes MongoDB data through REST API endpoints
"""
from fastapi import <PERSON>AP<PERSON>, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime
import sys
import os

# Add parent directory to path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mongo_data_manager import MongoDataManager

app = FastAPI(
    title="Sports Betting Analysis API",
    description="REST API for sports betting data analysis",
    version="1.0.0"
)

# Enable CORS for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # React dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize MongoDB connection
dm = MongoDataManager()

# Pydantic models for request/response
class URLPreviewRequest(BaseModel):
    url: str

class StoreGameRequest(BaseModel):
    url: str
    selected_stats: Optional[Dict[str, List[str]]] = None

class PlayerSearchResponse(BaseModel):
    id: str
    name: str
    sport: str
    recent_game: Optional[datetime] = None

class TeamSearchResponse(BaseModel):
    id: str
    name: str
    abbreviation: str
    sport: str
    games_count: int

class GameResponse(BaseModel):
    id: str
    espn_id: str
    date: Optional[datetime]
    home_team: str
    away_team: str
    score: str
    sport: str
    url: Optional[str] = None

class PlayerStatsResponse(BaseModel):
    player_name: str
    sport: str
    games_played: int
    stats: Dict[str, Dict[str, float]]

class DatabaseStatsResponse(BaseModel):
    total_games: int
    total_players: int
    total_teams: int
    total_player_stats: int
    sports: List[str]
    connection_status: str

# API Endpoints

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Sports Betting Analysis API", "status": "running"}

@app.get("/api/stats/database", response_model=DatabaseStatsResponse)
async def get_database_stats():
    """Get overall database statistics"""
    try:
        stats = dm.get_database_stats()
        return DatabaseStatsResponse(**stats)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/preview")
async def preview_url(request: URLPreviewRequest):
    """Preview what data can be extracted from ESPN URL"""
    try:
        preview = dm.preview_url(request.url)
        return preview
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/preview/detailed")
async def preview_url_detailed(request: URLPreviewRequest):
    """Preview URL with actual player stats data"""
    try:
        detailed_preview = dm.preview_url_with_stats(request.url)
        return detailed_preview
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/games/store")
async def store_game(request: StoreGameRequest):
    """Store game data from ESPN URL"""
    try:
        result = dm.store_game_from_url(request.url, request.selected_stats)
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/games", response_model=List[GameResponse])
async def get_games(
    sport: Optional[str] = Query(None, description="Filter by sport"),
    limit: int = Query(50, description="Maximum number of games to return")
):
    """Get list of stored games"""
    try:
        games = dm.list_stored_games(sport, limit)
        return [GameResponse(**game) for game in games]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/players/search", response_model=List[PlayerSearchResponse])
async def search_players(
    query: str = Query(..., description="Search query for player names"),
    sport: Optional[str] = Query(None, description="Filter by sport"),
    limit: int = Query(20, description="Maximum number of results")
):
    """Search for players by name"""
    try:
        players = dm.search_players(query, sport, limit)
        return [PlayerSearchResponse(**player) for player in players]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/teams/search", response_model=List[TeamSearchResponse])
async def search_teams(
    query: str = Query(..., description="Search query for team names"),
    sport: Optional[str] = Query(None, description="Filter by sport"),
    limit: int = Query(20, description="Maximum number of results")
):
    """Search for teams by name or abbreviation"""
    try:
        teams = dm.search_teams(query, sport, limit)
        return [TeamSearchResponse(**team) for team in teams]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/players/{player_id}/stats", response_model=PlayerStatsResponse)
async def get_player_stats(
    player_id: str,
    stat_types: Optional[List[str]] = Query(None, description="Specific stats to include")
):
    """Get statistical summary for a player"""
    try:
        stats = dm.get_player_stats_summary(player_id, stat_types)
        if "error" in stats:
            raise HTTPException(status_code=404, detail=stats["error"])
        return PlayerStatsResponse(**stats)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/sports")
async def get_available_sports():
    """Get list of available sports and their betting stats"""
    try:
        sports_data = {}
        for sport in ['mlb', 'nhl', 'nba', 'nfl']:
            sports_data[sport] = dm.get_available_stats(sport)
        return sports_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/sports/{sport}/stats")
async def get_sport_stats(sport: str):
    """Get available betting stats for a specific sport"""
    try:
        stats = dm.get_available_stats(sport)
        if not stats:
            raise HTTPException(status_code=404, detail=f"Sport '{sport}' not found")
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Recommended stats endpoints (sport-specific)
@app.get("/api/mlb/recommended-stats")
async def get_mlb_recommended_stats():
    """Get your recommended MLB betting stats"""
    return {
        "player_stats": ["hits", "home_runs", "rbis", "strikeouts_pitcher", "hits_allowed"],
        "description": "Key MLB betting stats: batter hits, home runs, RBIs, pitcher strikeouts, pitcher hits allowed"
    }

@app.get("/api/nba/recommended-stats")
async def get_nba_recommended_stats():
    """Get recommended NBA betting stats"""
    return {
        "player_stats": ["points", "rebounds", "assists", "steals", "blocks", "turnovers", "plus_minus"],
        "team_stats": ["points", "rebounds", "assists", "turnovers", "steals", "blocks"],
        "description": "Key NBA betting stats: points, rebounds, assists, steals, blocks, turnovers, plus/minus for defensive performance"
    }

# Analytics endpoints
@app.get("/api/analytics/top-performers")
async def get_top_performers(
    sport: str = Query(..., description="Sport to analyze"),
    stat: str = Query(..., description="Stat to rank by"),
    limit: int = Query(10, description="Number of top performers")
):
    """Get top performers for a specific stat"""
    try:
        # This would need custom implementation based on your analytics needs
        return {"message": "Analytics endpoint - to be implemented"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/analytics/predict-game")
async def predict_game(request: dict):
    """Predict game outcome between two teams"""
    try:
        team1_name = request.get('team1')
        team2_name = request.get('team2')
        sport = request.get('sport')

        if not all([team1_name, team2_name, sport]):
            raise HTTPException(status_code=400, detail="Missing required fields: team1, team2, sport")

        prediction = dm.predict_game_outcome(team1_name, team2_name, sport)
        return prediction
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/analytics/team-averages")
async def get_team_averages(
    team_name: str = Query(..., description="Team name"),
    sport: str = Query(..., description="Sport"),
    last_n_games: int = Query(10, description="Number of recent games to analyze")
):
    """Get team's average stats from recent games"""
    try:
        averages = dm.get_team_averages(team_name, sport, last_n_games)
        return averages
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up database connections on shutdown"""
    dm.close_connection()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
