"""
Database models for sports betting analysis system
"""
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Boolean, ForeignKey, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime

Base = declarative_base()

class Team(Base):
    __tablename__ = 'teams'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    abbreviation = Column(String(10), nullable=False)
    conference = Column(String(50))
    division = Column(String(50))
    city = Column(String(100))
    sport = Column(String(20))  # nhl, nba, nfl, mlb
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    home_games = relationship("Game", foreign_keys="Game.home_team_id", back_populates="home_team")
    away_games = relationship("Game", foreign_keys="Game.away_team_id", back_populates="away_team")
    players = relationship("Player", back_populates="team")

class Player(Base):
    __tablename__ = 'players'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    position = Column(String(20))
    jersey_number = Column(Integer)
    team_id = Column(Integer, ForeignKey('teams.id'))
    height = Column(String(10))
    weight = Column(Integer)
    age = Column(Integer)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    team = relationship("Team", back_populates="players")
    stats = relationship("PlayerStat", back_populates="player")

class Game(Base):
    __tablename__ = 'games'

    id = Column(Integer, primary_key=True)
    espn_game_id = Column(String(50), unique=True)
    date = Column(DateTime, nullable=False)
    home_team_id = Column(Integer, ForeignKey('teams.id'), nullable=False)
    away_team_id = Column(Integer, ForeignKey('teams.id'), nullable=False)
    home_score = Column(Integer)
    away_score = Column(Integer)
    status = Column(String(20))  # scheduled, in_progress, final
    season = Column(String(10))  # e.g., "2023-24"
    week = Column(Integer)
    venue = Column(String(100))
    attendance = Column(Integer)
    sport = Column(String(20))  # nhl, nba, nfl, mlb
    url = Column(String(500))  # ESPN URL for this game
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    home_team = relationship("Team", foreign_keys=[home_team_id], back_populates="home_games")
    away_team = relationship("Team", foreign_keys=[away_team_id], back_populates="away_games")
    player_stats = relationship("PlayerStat", back_populates="game")
    team_stats = relationship("TeamStat", back_populates="game")

class PlayerStat(Base):
    __tablename__ = 'player_stats'

    id = Column(Integer, primary_key=True)
    game_id = Column(Integer, ForeignKey('games.id'), nullable=False)
    player_id = Column(Integer, ForeignKey('players.id'), nullable=False)

    # Basketball stats
    minutes_played = Column(Float)
    points = Column(Integer)
    rebounds = Column(Integer)
    assists = Column(Integer)
    steals = Column(Integer)
    blocks = Column(Integer)
    turnovers = Column(Integer)
    fouls = Column(Integer)
    field_goals_made = Column(Integer)
    field_goals_attempted = Column(Integer)
    three_pointers_made = Column(Integer)
    three_pointers_attempted = Column(Integer)
    free_throws_made = Column(Integer)
    free_throws_attempted = Column(Integer)
    plus_minus = Column(Integer)  # NBA +/- stat

    # Football stats (can be null for basketball)
    passing_yards = Column(Integer)
    passing_touchdowns = Column(Integer)
    interceptions = Column(Integer)
    rushing_yards = Column(Integer)
    rushing_touchdowns = Column(Integer)
    receiving_yards = Column(Integer)
    receiving_touchdowns = Column(Integer)
    receptions = Column(Integer)

    # Baseball stats
    hits_allowed = Column(Integer)  # For pitchers

    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    game = relationship("Game", back_populates="player_stats")
    player = relationship("Player", back_populates="stats")

class TeamStat(Base):
    __tablename__ = 'team_stats'

    id = Column(Integer, primary_key=True)
    game_id = Column(Integer, ForeignKey('games.id'), nullable=False)
    team_id = Column(Integer, ForeignKey('teams.id'), nullable=False)

    # General stats
    points = Column(Integer)
    field_goal_percentage = Column(Float)
    three_point_percentage = Column(Float)
    free_throw_percentage = Column(Float)
    rebounds = Column(Integer)
    assists = Column(Integer)
    turnovers = Column(Integer)
    steals = Column(Integer)
    blocks = Column(Integer)
    fouls = Column(Integer)

    # Advanced stats
    possession_time = Column(Float)
    pace = Column(Float)
    offensive_rating = Column(Float)
    defensive_rating = Column(Float)

    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    game = relationship("Game", back_populates="team_stats")
    team = relationship("Team")

class BettingLine(Base):
    __tablename__ = 'betting_lines'

    id = Column(Integer, primary_key=True)
    game_id = Column(Integer, ForeignKey('games.id'), nullable=False)
    sportsbook = Column(String(50))
    spread_home = Column(Float)
    spread_away = Column(Float)
    total_over_under = Column(Float)
    moneyline_home = Column(Integer)
    moneyline_away = Column(Integer)
    timestamp = Column(DateTime, default=datetime.utcnow)

    # Relationships
    game = relationship("Game")

# Database setup functions
def create_database(db_path="sports_betting.db"):
    """Create database and all tables"""
    engine = create_engine(f'sqlite:///{db_path}')
    Base.metadata.create_all(engine)
    return engine

def get_session(engine):
    """Get database session"""
    Session = sessionmaker(bind=engine)
    return Session()
